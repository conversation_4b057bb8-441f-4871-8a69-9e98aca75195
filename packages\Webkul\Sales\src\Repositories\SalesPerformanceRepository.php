<?php

namespace Webkul\Sales\Repositories;

use Illuminate\Support\Collection;
use Webkul\Core\Eloquent\Repository;
use Webkul\Sales\Models\SalesPerformance;

class SalesPerformanceRepository extends Repository
{
    /**
     * Specify Model class name
     */
    public function model(): string
    {
        return SalesPerformance::class;
    }

    /**
     * Get performance summary.
     */
    public function getPerformanceSummary(string $period = 'monthly', string $dateFrom = null, string $dateTo = null): array
    {
        $query = $this->model->query();

        if ($dateFrom && $dateTo) {
            $query->whereBetween('period_start', [$dateFrom, $dateTo]);
        }

        if ($period) {
            $query->where('period_type', $period);
        }

        $totalRecords = $query->count();
        $totalTargetAmount = $query->sum('target_amount');
        $totalAchievedAmount = $query->sum('achieved_amount');
        $averageAchievement = $query->avg('achievement_percentage');
        $averageConversion = $query->avg('conversion_rate');

        return [
            'total_records'         => $totalRecords,
            'total_target_amount'   => $totalTargetAmount,
            'total_achieved_amount' => $totalAchievedAmount,
            'overall_achievement'   => $totalTargetAmount > 0 ? 
                round(($totalAchievedAmount / $totalTargetAmount) * 100, 2) : 0,
            'average_achievement'   => round($averageAchievement, 2),
            'average_conversion'    => round($averageConversion, 2),
        ];
    }

    /**
     * Get leaderboard data.
     */
    public function getLeaderboard(string $type = 'individual', string $period = 'monthly', int $limit = 10, array $filters = []): Collection
    {
        $query = $this->model->query()
            ->where('entity_type', $type)
            ->where('period_type', $period);

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->where('period_start', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('period_end', '<=', $filters['date_to']);
        }

        // Get latest period for each entity
        $query->whereIn('id', function ($subQuery) use ($type, $period) {
            $subQuery->select('id')
                     ->from('sales_performance')
                     ->where('entity_type', $type)
                     ->where('period_type', $period)
                     ->whereRaw('period_start = (SELECT MAX(period_start) FROM sales_performance sp2 WHERE sp2.entity_id = sales_performance.entity_id AND sp2.entity_type = sales_performance.entity_type AND sp2.period_type = sales_performance.period_type)');
        });

        return $query->orderBy('score', 'desc')
                    ->orderBy('achievement_percentage', 'desc')
                    ->limit($limit)
                    ->get();
    }

    /**
     * Get performance over time.
     */
    public function getPerformanceOverTime(string $period = 'monthly', string $dateFrom = null, string $dateTo = null): Collection
    {
        $query = $this->model->query()
            ->selectRaw('
                period_start,
                period_end,
                SUM(target_amount) as total_target,
                SUM(achieved_amount) as total_achieved,
                AVG(achievement_percentage) as avg_achievement,
                AVG(conversion_rate) as avg_conversion,
                COUNT(*) as record_count
            ')
            ->where('period_type', $period)
            ->groupBy('period_start', 'period_end')
            ->orderBy('period_start');

        if ($dateFrom && $dateTo) {
            $query->whereBetween('period_start', [$dateFrom, $dateTo]);
        }

        return $query->get();
    }

    /**
     * Get target vs actual data.
     */
    public function getTargetVsActual(string $entityType = 'individual', string $period = 'monthly', string $dateFrom = null, string $dateTo = null): Collection
    {
        $query = $this->model->query()
            ->select([
                'entity_name',
                'target_amount',
                'achieved_amount',
                'achievement_percentage',
                'period_start',
                'period_end'
            ])
            ->where('entity_type', $entityType)
            ->where('period_type', $period);

        if ($dateFrom && $dateTo) {
            $query->whereBetween('period_start', [$dateFrom, $dateTo]);
        }

        return $query->orderBy('period_start')
                    ->orderBy('achievement_percentage', 'desc')
                    ->get();
    }

    /**
     * Get achievement rates.
     */
    public function getAchievementRates(string $period = 'monthly', string $dateFrom = null, string $dateTo = null): array
    {
        $query = $this->model->query()
            ->where('period_type', $period);

        if ($dateFrom && $dateTo) {
            $query->whereBetween('period_start', [$dateFrom, $dateTo]);
        }

        $total = $query->count();
        $achieved100 = $query->where('achievement_percentage', '>=', 100)->count();
        $achieved75 = $query->where('achievement_percentage', '>=', 75)->where('achievement_percentage', '<', 100)->count();
        $achieved50 = $query->where('achievement_percentage', '>=', 50)->where('achievement_percentage', '<', 75)->count();
        $below50 = $query->where('achievement_percentage', '<', 50)->count();

        return [
            'total'       => $total,
            'achieved_100' => $achieved100,
            'achieved_75'  => $achieved75,
            'achieved_50'  => $achieved50,
            'below_50'     => $below50,
            'rates' => [
                '100%+' => $total > 0 ? round(($achieved100 / $total) * 100, 2) : 0,
                '75-99%' => $total > 0 ? round(($achieved75 / $total) * 100, 2) : 0,
                '50-74%' => $total > 0 ? round(($achieved50 / $total) * 100, 2) : 0,
                '<50%' => $total > 0 ? round(($below50 / $total) * 100, 2) : 0,
            ],
        ];
    }

    /**
     * Get conversion rates over time.
     */
    public function getConversionRates(string $period = 'monthly', string $dateFrom = null, string $dateTo = null): Collection
    {
        $query = $this->model->query()
            ->selectRaw('
                period_start,
                AVG(conversion_rate) as avg_conversion_rate,
                SUM(leads_count) as total_leads,
                SUM(won_leads_count) as total_won_leads
            ')
            ->where('period_type', $period)
            ->groupBy('period_start')
            ->orderBy('period_start');

        if ($dateFrom && $dateTo) {
            $query->whereBetween('period_start', [$dateFrom, $dateTo]);
        }

        return $query->get();
    }

    /**
     * Get trends for a specific metric.
     */
    public function getTrends(string $metric = 'achievement_percentage', string $period = 'daily', string $dateFrom = null, string $dateTo = null): Collection
    {
        $query = $this->model->query()
            ->selectRaw("
                period_start,
                AVG({$metric}) as avg_value,
                MIN({$metric}) as min_value,
                MAX({$metric}) as max_value
            ")
            ->where('period_type', $period)
            ->groupBy('period_start')
            ->orderBy('period_start');

        if ($dateFrom && $dateTo) {
            $query->whereBetween('period_start', [$dateFrom, $dateTo]);
        }

        return $query->get();
    }

    /**
     * Update performance rankings.
     */
    public function updateRankings(string $entityType = 'individual', string $period = 'monthly'): void
    {
        $performances = $this->model->query()
            ->where('entity_type', $entityType)
            ->where('period_type', $period)
            ->whereRaw('period_start = (SELECT MAX(period_start) FROM sales_performance sp2 WHERE sp2.entity_type = ? AND sp2.period_type = ?)', [$entityType, $period])
            ->orderBy('score', 'desc')
            ->orderBy('achievement_percentage', 'desc')
            ->get();

        $rank = 1;
        foreach ($performances as $performance) {
            $performance->update(['rank' => $rank]);
            $rank++;
        }
    }
}
