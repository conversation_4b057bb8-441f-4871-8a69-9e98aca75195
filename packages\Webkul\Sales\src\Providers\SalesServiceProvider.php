<?php

namespace Webkul\Sales\Providers;

use Illuminate\Routing\Router;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;

class SalesServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap services.
     */
    public function boot(Router $router): void
    {
        $this->loadMigrationsFrom(__DIR__.'/../Database/Migrations');

        $this->loadTranslationsFrom(__DIR__.'/../Resources/lang', 'sales');

        $this->loadViewsFrom(__DIR__.'/../Resources/views', 'sales');

        Route::middleware(['web', 'admin_locale', 'user'])
            ->prefix(config('app.admin_path'))
            ->group(__DIR__.'/../Routes/web.php');

        $this->publishes([
            __DIR__.'/../Resources/assets' => public_path('vendor/sales'),
        ], 'sales-assets');
    }

    /**
     * Register services.
     */
    public function register(): void
    {
        $this->registerConfig();
        $this->registerRepositories();
    }

    /**
     * Register package config.
     */
    protected function registerConfig(): void
    {
        $this->mergeConfigFrom(dirname(__DIR__).'/Config/menu.php', 'menu.admin');
        $this->mergeConfigFrom(dirname(__DIR__).'/Config/acl.php', 'acl');
        $this->mergeConfigFrom(dirname(__DIR__).'/Config/breadcrumbs.php', 'breadcrumbs');
    }

    /**
     * Register repositories.
     */
    protected function registerRepositories(): void
    {
        $this->app->bind(
            \Webkul\Sales\Repositories\SalesTargetRepository::class
        );

        $this->app->bind(
            \Webkul\Sales\Repositories\SalesPerformanceRepository::class
        );

        $this->app->bind(
            \Webkul\Sales\Repositories\SalesReportRepository::class
        );
    }
}
